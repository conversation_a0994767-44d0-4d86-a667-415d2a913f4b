common:
    lid_topic:  "/os_cloud_node/points"
    imu_topic:  "/os_cloud_node/imu"
    con_frame: false # true: if you need to combine several LiDAR frames into one
    con_frame_num: 1 # the number of frames combined
    cut_frame: false # true: if you need to cut one LiDAR frame into several subframes
    cut_frame_time_interval: 0.1 # should be integral fraction of 1 / LiDAR frequency 
    time_diff_lidar_to_imu: 0.0 # Time offset between LiDAR and IMU calibrated by other algorithms, e.g., LI-Init (find in Readme)

preprocess:
    lidar_type: 3 # 2 #velodyne                # 1 Livox Avia LiDAR
    scan_line: 64 # 32 #velodyne 6 avia
    timestamp_unit: 3           # the unit of time/t field in the PointCloud2 rostopic: 0-second, 1-milisecond, 2-microsecond, 3-nanosecond.
    blind: 4.0

mapping:
    imu_en: true
    extrinsic_est_en: false # for aggressive motion, set this variable false
    imu_time_inte: 0.01 # = 1 / frequency of IMU
    lidar_time_inte: 0.1
    satu_acc: 30.0 # the saturation value of IMU's acceleration. not related to the units
    satu_gyro: 35 # the saturation value of IMU's angular velocity. not related to the units
    acc_norm: 9.81 # 1.0 for g as unit, 9.81 for m/s^2 as unit of the IMU's acceleration
    lidar_meas_cov: 0.01 # 0.001
    acc_cov_output: 500
    gyr_cov_output: 1000 
    b_acc_cov: 0.0001 
    b_gyr_cov: 0.0001 
    imu_meas_acc_cov: 0.1 #0.1 # 2
    imu_meas_omg_cov: 0.1 #0.1 # 2 
    gyr_cov_input: 0.01 # for IMU as input model
    acc_cov_input: 0.1 # for IMU as input model
    plane_thr: 0.1 # 0.05, the threshold for plane criteria, the smaller, the flatter a plane
    match_s: 81
    ivox_grid_resolution: 2.0
    gravity: [0.0, 0.0, -9.810] # preknown gravity, use when imu_en is false or start from a non-stationary state
    gravity_init: [0.0, 0.0, -9.810] # preknown gravity in the initial IMU frame for unstationary start or in the initial LiDAR frame for using without IMU
    extrinsic_T: [0.0, 0.0, 0.0]
    extrinsic_R: [ 1, 0, 0,
                   0, 1, 0,
                   0, 0, 1 ]

odometry: 
    publish_odometry_without_downsample: false

publish:
    path_en: true                 # false: close the path output
    scan_publish_en: true         # false: close all the point cloud output
    scan_bodyframe_pub_en: false  # true: output the point cloud scans in IMU-body-frame

pcd_save:
    pcd_save_en: false
    interval: -1                 # how many LiDAR frames saved in each pcd file; 
                                 # -1 : all frames will be saved in ONE pcd file, may lead to memory crash when having too much frames.