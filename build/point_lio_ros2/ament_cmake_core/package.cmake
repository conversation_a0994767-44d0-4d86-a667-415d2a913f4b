set(_AMENT_PACKAGE_NAME "point_lio_ros2")
set(point_lio_ros2_VERSION "0.0.0")
set(point_lio_ros2_MAINTAINER "<PERSON> <<EMAIL>>")
set(point_lio_ros2_BUILD_DEPENDS "rclcpp" "rclpy" "sensor_msgs" "geometry_msgs" "nav_msgs" "tf2_ros" "pcl_ros" "pcl_conversions" "visualization_msgs")
set(point_lio_ros2_BUILDTOOL_DEPENDS "ament_cmake")
set(point_lio_ros2_BUILD_EXPORT_DEPENDS "rclcpp" "rclpy" "sensor_msgs" "geometry_msgs" "nav_msgs" "tf2_ros" "pcl_ros" "pcl_conversions" "visualization_msgs")
set(point_lio_ros2_BUILDTOOL_EXPORT_DEPENDS )
set(point_lio_ros2_EXEC_DEPENDS "rclcpp" "rclpy" "sensor_msgs" "geometry_msgs" "nav_msgs" "tf2_ros" "pcl_ros" "pcl_conversions" "visualization_msgs")
set(point_lio_ros2_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(point_lio_ros2_GROUP_DEPENDS )
set(point_lio_ros2_MEMBER_OF_GROUPS )
set(point_lio_ros2_DEPRECATED "")
set(point_lio_ros2_EXPORT_TAGS)
list(APPEND point_lio_ros2_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
