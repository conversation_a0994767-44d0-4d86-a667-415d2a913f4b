{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-8d9f43ae77506a79b994.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "point_lio_ros2", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "point_lio_ros2_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-point_lio_ros2_uninstall-58d4b624cf463b7cfd69.json", "name": "point_lio_ros2_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "pointlio_mapping::@6890427a1f51a3e7e1df", "jsonFile": "target-pointlio_mapping-88a7f87ab003446dd5a7.json", "name": "pointlio_mapping", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-920ba73a7ccaa81558c8.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2", "source": "/home/<USER>/code/ros2_ws/pointlio/src/point_lio_ros2"}, "version": {"major": 2, "minor": 3}}