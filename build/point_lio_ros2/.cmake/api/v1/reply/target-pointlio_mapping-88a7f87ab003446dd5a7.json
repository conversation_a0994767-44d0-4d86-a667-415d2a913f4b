{"artifacts": [{"path": "pointlio_mapping"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "ament_target_dependencies", "add_compile_options", "target_compile_definitions", "add_definitions", "include_directories", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 71, "parent": 0}, {"command": 2, "file": 0, "line": 72, "parent": 0}, {"command": 1, "file": 1, "line": 145, "parent": 2}, {"command": 1, "file": 1, "line": 151, "parent": 2}, {"command": 3, "file": 0, "line": 14, "parent": 0}, {"command": 4, "file": 1, "line": 128, "parent": 2}, {"command": 5, "file": 0, "line": 23, "parent": 0}, {"command": 5, "file": 0, "line": 24, "parent": 0}, {"command": 5, "file": 0, "line": 13, "parent": 0}, {"command": 6, "file": 0, "line": 64, "parent": 0}, {"command": 7, "file": 1, "line": 141, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -fopenmp"}, {"backtrace": 5, "fragment": "-O3"}, {"backtrace": 5, "fragment": "-pthread"}, {"backtrace": 5, "fragment": "-fexceptions"}, {"backtrace": 3, "fragment": "-fPIC"}, {"fragment": "-std=c++17"}], "defines": [{"backtrace": 3, "define": "BOOST_ALL_NO_LIB"}, {"backtrace": 3, "define": "BOOST_DATE_TIME_DYN_LINK"}, {"backtrace": 3, "define": "BOOST_FILESYSTEM_DYN_LINK"}, {"backtrace": 3, "define": "BOOST_IOSTREAMS_DYN_LINK"}, {"backtrace": 3, "define": "BOOST_SERIALIZATION_DYN_LINK"}, {"backtrace": 3, "define": "BOOST_SYSTEM_DYN_LINK"}, {"backtrace": 3, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 6, "define": "DISABLE_PCAP"}, {"backtrace": 7, "define": "MP_EN"}, {"backtrace": 8, "define": "MP_PROC_NUM=4"}, {"backtrace": 3, "define": "QT_CORE_LIB"}, {"backtrace": 3, "define": "QT_GUI_LIB"}, {"backtrace": 3, "define": "QT_NO_DEBUG"}, {"backtrace": 3, "define": "QT_OPENGL_LIB"}, {"backtrace": 3, "define": "QT_WIDGETS_LIB"}, {"backtrace": 3, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"backtrace": 9, "define": "ROOT_DIR=\"/home/<USER>/code/ros2_ws/pointlio/src/point_lio_ros2/\""}, {"backtrace": 3, "define": "kiss_fft_scalar=double"}], "includes": [{"backtrace": 10, "path": "/home/<USER>/code/ros2_ws/pointlio/src/point_lio_ros2/include"}, {"backtrace": 10, "path": "/usr/include/python3.10"}, {"backtrace": 10, "isSystem": true, "path": "/usr/include/eigen3"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/humble/include/nav_msgs"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/humble/include/pcl_ros"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/humble/include/pcl_conversions"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/humble/include/message_filters"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/humble/include/pcl_msgs"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/humble/include/tf2_ros"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/humble/include/visualization_msgs"}, {"backtrace": 11, "isSystem": true, "path": "/usr/include/pcl-1.12"}, {"backtrace": 11, "isSystem": true, "path": "/usr/include/ni"}, {"backtrace": 11, "isSystem": true, "path": "/usr/include/openni2"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/tf2"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/tf2_geometry_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_action"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/action_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/unique_identifier_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rcl_action"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/tf2_msgs"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/vtk-9.1"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/jsoncpp"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/freetype2"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt5"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt5/QtOpenGL"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt5/QtWidgets"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt5/QtGui"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt5/QtCore"}, {"backtrace": 3, "isSystem": true, "path": "/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_components"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/class_loader"}, {"backtrace": 3, "isSystem": true, "path": "/opt/ros/humble/include/composition_interfaces"}], "language": "CXX", "languageStandard": {"backtraces": [3, 3, 3, 3], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4]}], "id": "pointlio_mapping::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-fopenmp", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/opt/ros/humble/lib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libpcl_ros_tf.a", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libpcd_to_pointcloud_lib.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libstatic_transform_broadcaster_node.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libmessage_filters.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 4, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_apps.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_outofcore.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_people.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/libOpenNI.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libOpenNI2.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libusb-1.0.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libflann_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libnav_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libtf2_ros.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libmessage_filters.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librclcpp_action.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_action.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtf2.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/liborocos-kdl.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libpcl_common.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "-llz4", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libqhull_r.so.8.0.2", "role": "libraries"}, {"fragment": "-lm", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libmessage_filters.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libpcl_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcomponent_manager.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libclass_loader.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libconsole_bridge.so.1.0", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcomposition_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_surface.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_keypoints.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_tracking.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_recognition.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_registration.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_stereo.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_segmentation.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_features.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_filters.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_ml.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_visualization.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_search.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_kdtree.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_io.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_octree.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libpng.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libz.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/libOpenNI.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libOpenNI2.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libusb-1.0.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkChartsCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkInteractionImage-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkIOGeometry-9.1.so.9.1.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libjsoncpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkIOPLY-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkRenderingLOD-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkViewsContext2D-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkViewsCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkGUISupportQt-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkInteractionWidgets-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkFiltersModeling-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkInteractionStyle-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkFiltersExtraction-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkIOLegacy-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkIOCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkRenderingAnnotation-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkRenderingContext2D-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkRenderingFreeType-9.1.so.9.1.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libfreetype.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkImagingSources-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkIOImage-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkImagingCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkRenderingOpenGL2-9.1.so.9.1.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libvtkRenderingUI-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkRenderingCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkCommonColor-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkFiltersGeometry-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkFiltersSources-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkFiltersGeneral-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkCommonComputationalGeometry-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkFiltersCore-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkCommonExecutionModel-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkCommonDataModel-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkCommonMisc-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkCommonTransforms-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkCommonMath-9.1.so.9.1.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libvtkkissfft-9.1.so.9.1.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libGLEW.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libX11.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libQt5OpenGL.so.5.15.3", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libQt5Widgets.so.5.15.3", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libQt5Gui.so.5.15.3", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libQt5Core.so.5.15.3", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libvtkCommonCore-9.1.so.9.1.0", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libtbb.so.12.5", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libvtksys-9.1.so.9.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libpcl_common.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libboost_system.so.1.74.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.74.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.74.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libboost_iostreams.so.1.74.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libboost_serialization.so.1.74.0", "role": "libraries"}], "language": "CXX"}, "name": "pointlio_mapping", "nameOnDisk": "pointlio_mapping", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/laserMapping.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "include/ikd-Tree/ikd_Tree.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/parameters.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/preprocess.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Estimator.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}