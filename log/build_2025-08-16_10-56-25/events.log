[0.000000] (-) TimerEvent: {}
[0.000199] (-) JobUnselected: {'identifier': 'point_lio'}
[0.000231] (point_lio_ros2) JobQueued: {'identifier': 'point_lio_ros2', 'dependencies': OrderedDict()}
[0.000250] (point_lio_ros2) JobStarted: {'identifier': 'point_lio_ros2'}
[0.016704] (point_lio_ros2) JobProgress: {'identifier': 'point_lio_ros2', 'progress': 'cmake'}
[0.017280] (point_lio_ros2) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/code/ros2_ws/pointlio/src/point_lio_ros2', '-DAMENT_CMAKE_SYMLINK_INSTALL=1', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2'], 'cwd': '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7897/'), ('no_proxy', 'localhost,127.0.0.1,***********/16,10.0.0.0/8,**********/12,::1'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'wuliu'), ('all_proxy', 'socks://127.0.0.1:7897/'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('TERM_PROGRAM_VERSION', '1.103.1'), ('DESKTOP_SESSION', 'ubuntu'), ('NO_PROXY', 'localhost,127.0.0.1,***********/16,10.0.0.0/8,**********/12,::1'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('SYSTEMD_EXEC_PID', '17594'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('https_proxy', 'http://127.0.0.1:7897/'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'wuliu'), ('ALL_PROXY', 'socks://127.0.0.1:7897/'), ('http_proxy', 'http://127.0.0.1:7897/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'wuliu'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/wuliu:@/tmp/.ICE-unix/1331,unix/wuliu:/tmp/.ICE-unix/1331'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/6d8d3855_5aa0_48a5_9891_86d77e43fc8e'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.ARYRA3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-2157ec0450.sock'), ('GNOME_TERMINAL_SERVICE', ':1.132'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7897/'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.098904] (-) TimerEvent: {}
[0.106713] (point_lio_ros2) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.179524] (point_lio_ros2) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.188888] (point_lio_ros2) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.199124] (-) TimerEvent: {}
[0.272515] (point_lio_ros2) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.278280] (point_lio_ros2) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.278596] (point_lio_ros2) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.279142] (point_lio_ros2) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.281824] (point_lio_ros2) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.299257] (-) TimerEvent: {}
[0.370449] (point_lio_ros2) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.375861] (point_lio_ros2) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.376081] (point_lio_ros2) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.376403] (point_lio_ros2) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.376954] (point_lio_ros2) StdoutLine: {'line': b'-- Current CPU architecture: x86_64\n'}
[0.383720] (point_lio_ros2) StdoutLine: {'line': b'-- Processor number:  16\n'}
[0.383949] (point_lio_ros2) StdoutLine: {'line': b'-- core for MP:  3\n'}
[0.388601] (point_lio_ros2) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.399409] (-) TimerEvent: {}
[0.499823] (-) TimerEvent: {}
[0.544588] (point_lio_ros2) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[0.599944] (-) TimerEvent: {}
[0.601761] (point_lio_ros2) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[0.627225] (point_lio_ros2) StdoutLine: {'line': b'-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.664751] (point_lio_ros2) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.668710] (point_lio_ros2) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.676332] (point_lio_ros2) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.687257] (point_lio_ros2) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.700059] (-) TimerEvent: {}
[0.701006] (point_lio_ros2) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.739898] (point_lio_ros2) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.742478] (point_lio_ros2) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.800145] (-) TimerEvent: {}
[0.834502] (point_lio_ros2) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  \n'}
[0.857290] (point_lio_ros2) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include  \n'}
[0.894340] (point_lio_ros2) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.900233] (-) TimerEvent: {}
[0.903887] (point_lio_ros2) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[0.977619] (point_lio_ros2) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[0.977985] (point_lio_ros2) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[1.000323] (-) TimerEvent: {}
[1.049660] (point_lio_ros2) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[1.050508] (point_lio_ros2) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[1.094949] (point_lio_ros2) StdoutLine: {'line': b'-- Found rclpy: 3.3.17 (/opt/ros/humble/share/rclpy/cmake)\n'}
[1.096066] (point_lio_ros2) StdoutLine: {'line': b'-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)\n'}
[1.100422] (-) TimerEvent: {}
[1.108548] (point_lio_ros2) StdoutLine: {'line': b'-- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)\n'}
[1.125089] (point_lio_ros2) StdoutLine: {'line': b'-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[1.141352] (point_lio_ros2) StdoutLine: {'line': b'-- Found pcl_ros: 2.4.5 (/opt/ros/humble/share/pcl_ros/cmake)\n'}
[1.180988] (point_lio_ros2) StdoutLine: {'line': b"-- Checking for module 'eigen3'\n"}
[1.198375] (point_lio_ros2) StdoutLine: {'line': b'--   Found eigen3, version 3.4.0\n'}
[1.200527] (-) TimerEvent: {}
[1.236013] (point_lio_ros2) StdoutLine: {'line': b'-- Found Eigen: /usr/include/eigen3 (Required is at least version "3.1") \n'}
[1.236241] (point_lio_ros2) StdoutLine: {'line': b'-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)\n'}
[1.268901] (point_lio_ros2) StdoutLine: {'line': b"-- Checking for module 'flann'\n"}
[1.285780] (point_lio_ros2) StdoutLine: {'line': b'--   Found flann, version 1.9.1\n'}
[1.300666] (-) TimerEvent: {}
[1.329811] (point_lio_ros2) StdoutLine: {'line': b'-- Found FLANN: /usr/lib/x86_64-linux-gnu/libflann_cpp.so  \n'}
[1.329993] (point_lio_ros2) StdoutLine: {'line': b'-- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)\n'}
[1.400768] (-) TimerEvent: {}
[1.501034] (-) TimerEvent: {}
[1.601359] (-) TimerEvent: {}
[1.701687] (-) TimerEvent: {}
[1.802012] (-) TimerEvent: {}
[1.902338] (-) TimerEvent: {}
[2.002658] (-) TimerEvent: {}
[2.102982] (-) TimerEvent: {}
[2.203284] (-) TimerEvent: {}
[2.229517] (point_lio_ros2) StdoutLine: {'line': b"-- Checking for module 'libusb-1.0'\n"}
[2.244597] (point_lio_ros2) StdoutLine: {'line': b'--   Found libusb-1.0, version 1.0.25\n'}
[2.294820] (point_lio_ros2) StdoutLine: {'line': b'-- Found libusb: /usr/lib/x86_64-linux-gnu/libusb-1.0.so  \n'}
[2.296785] (point_lio_ros2) StdoutLine: {'line': b'-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)\n'}
[2.303362] (-) TimerEvent: {}
[2.365996] (point_lio_ros2) StdoutLine: {'line': b'-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)\n'}
[2.403474] (-) TimerEvent: {}
[2.489747] (point_lio_ros2) StdoutLine: {'line': b'-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)\n'}
[2.496550] (point_lio_ros2) StdoutLine: {'line': b'-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)\n'}
[2.503036] (point_lio_ros2) StdoutLine: {'line': b'-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)\n'}
[2.503546] (-) TimerEvent: {}
[2.603760] (-) TimerEvent: {}
[2.625866] (point_lio_ros2) StdoutLine: {'line': b'-- Found Qhull version 8.0.2\n'}
[2.703888] (-) TimerEvent: {}
[2.743198] (point_lio_ros2) StdoutLine: {'line': b'-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)\n'}
[2.804023] (-) TimerEvent: {}
[2.855573] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_COMMON: /usr/lib/x86_64-linux-gnu/libpcl_common.so  \n'}
[2.856325] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_KDTREE: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so  \n'}
[2.856873] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_OCTREE: /usr/lib/x86_64-linux-gnu/libpcl_octree.so  \n'}
[2.857401] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_SEARCH: /usr/lib/x86_64-linux-gnu/libpcl_search.so  \n'}
[2.858008] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_SAMPLE_CONSENSUS: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so  \n'}
[2.858589] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_FILTERS: /usr/lib/x86_64-linux-gnu/libpcl_filters.so  \n'}
[2.858835] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_2D: /usr/include/pcl-1.12  \n'}
[2.859092] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_GEOMETRY: /usr/include/pcl-1.12  \n'}
[2.859643] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_IO: /usr/lib/x86_64-linux-gnu/libpcl_io.so  \n'}
[2.860261] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_FEATURES: /usr/lib/x86_64-linux-gnu/libpcl_features.so  \n'}
[2.860895] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_ML: /usr/lib/x86_64-linux-gnu/libpcl_ml.so  \n'}
[2.861550] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_SEGMENTATION: /usr/lib/x86_64-linux-gnu/libpcl_segmentation.so  \n'}
[2.862306] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_VISUALIZATION: /usr/lib/x86_64-linux-gnu/libpcl_visualization.so  \n'}
[2.862973] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_SURFACE: /usr/lib/x86_64-linux-gnu/libpcl_surface.so  \n'}
[2.863645] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_REGISTRATION: /usr/lib/x86_64-linux-gnu/libpcl_registration.so  \n'}
[2.864324] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_KEYPOINTS: /usr/lib/x86_64-linux-gnu/libpcl_keypoints.so  \n'}
[2.864963] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_TRACKING: /usr/lib/x86_64-linux-gnu/libpcl_tracking.so  \n'}
[2.865609] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_RECOGNITION: /usr/lib/x86_64-linux-gnu/libpcl_recognition.so  \n'}
[2.866452] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_STEREO: /usr/lib/x86_64-linux-gnu/libpcl_stereo.so  \n'}
[2.867038] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_APPS: /usr/lib/x86_64-linux-gnu/libpcl_apps.so  \n'}
[2.867562] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_IN_HAND_SCANNER: /usr/include/pcl-1.12  \n'}
[2.867785] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_MODELER: /usr/include/pcl-1.12  \n'}
[2.868117] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_POINT_CLOUD_EDITOR: /usr/include/pcl-1.12  \n'}
[2.868672] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_OUTOFCORE: /usr/lib/x86_64-linux-gnu/libpcl_outofcore.so  \n'}
[2.869325] (point_lio_ros2) StdoutLine: {'line': b'-- Found PCL_PEOPLE: /usr/lib/x86_64-linux-gnu/libpcl_people.so  \n'}
[2.904206] (-) TimerEvent: {}
[2.904660] (point_lio_ros2) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)\n'}
[2.905793] (point_lio_ros2) StdoutLine: {'line': b'-- Found Eigen3: TRUE (found version "3.4.0") \n'}
[2.906001] (point_lio_ros2) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[2.982787] (point_lio_ros2) StdoutLine: {'line': b'-- Found visualization_msgs: 4.9.0 (/opt/ros/humble/share/visualization_msgs/cmake)\n'}
[3.004324] (-) TimerEvent: {}
[3.104700] (-) TimerEvent: {}
[3.205041] (-) TimerEvent: {}
[3.305655] (-) TimerEvent: {}
[3.318368] (point_lio_ros2) StdoutLine: {'line': b'-- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found version "3.10.12") \n'}
[3.379094] (point_lio_ros2) StdoutLine: {'line': b'-- Configuring done\n'}
[3.405822] (-) TimerEvent: {}
[3.447669] (point_lio_ros2) StdoutLine: {'line': b'-- Generating done\n'}
[3.457363] (point_lio_ros2) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2\n'}
[3.474412] (point_lio_ros2) CommandEnded: {'returncode': 0}
[3.475424] (point_lio_ros2) JobProgress: {'identifier': 'point_lio_ros2', 'progress': 'build'}
[3.476609] (point_lio_ros2) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7897/'), ('no_proxy', 'localhost,127.0.0.1,***********/16,10.0.0.0/8,**********/12,::1'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'wuliu'), ('all_proxy', 'socks://127.0.0.1:7897/'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('TERM_PROGRAM_VERSION', '1.103.1'), ('DESKTOP_SESSION', 'ubuntu'), ('NO_PROXY', 'localhost,127.0.0.1,***********/16,10.0.0.0/8,**********/12,::1'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('SYSTEMD_EXEC_PID', '17594'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('https_proxy', 'http://127.0.0.1:7897/'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'wuliu'), ('ALL_PROXY', 'socks://127.0.0.1:7897/'), ('http_proxy', 'http://127.0.0.1:7897/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'wuliu'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/wuliu:@/tmp/.ICE-unix/1331,unix/wuliu:/tmp/.ICE-unix/1331'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/6d8d3855_5aa0_48a5_9891_86d77e43fc8e'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.ARYRA3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-2157ec0450.sock'), ('GNOME_TERMINAL_SERVICE', ':1.132'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7897/'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[3.505971] (-) TimerEvent: {}
[3.528865] (point_lio_ros2) StdoutLine: {'line': b'[ 83%] \x1b[32mBuilding CXX object CMakeFiles/pointlio_mapping.dir/include/ikd-Tree/ikd_Tree.cpp.o\x1b[0m\n'}
[3.529103] (point_lio_ros2) StdoutLine: {'line': b'[ 83%] \x1b[32mBuilding CXX object CMakeFiles/pointlio_mapping.dir/src/Estimator.cpp.o\x1b[0m\n'}
[3.529223] (point_lio_ros2) StdoutLine: {'line': b'[ 83%] \x1b[32mBuilding CXX object CMakeFiles/pointlio_mapping.dir/src/parameters.cpp.o\x1b[0m\n'}
[3.529326] (point_lio_ros2) StdoutLine: {'line': b'[ 83%] \x1b[32mBuilding CXX object CMakeFiles/pointlio_mapping.dir/src/preprocess.cpp.o\x1b[0m\n'}
[3.529421] (point_lio_ros2) StdoutLine: {'line': b'[ 83%] \x1b[32mBuilding CXX object CMakeFiles/pointlio_mapping.dir/src/laserMapping.cpp.o\x1b[0m\n'}
[3.606083] (-) TimerEvent: {}
[3.706470] (-) TimerEvent: {}
[3.806836] (-) TimerEvent: {}
[3.907190] (-) TimerEvent: {}
[4.007535] (-) TimerEvent: {}
[4.107898] (-) TimerEvent: {}
[4.208256] (-) TimerEvent: {}
[4.308675] (-) TimerEvent: {}
[4.409009] (-) TimerEvent: {}
[4.509392] (-) TimerEvent: {}
[4.609780] (-) TimerEvent: {}
[4.710261] (-) TimerEvent: {}
[4.810586] (-) TimerEvent: {}
[4.910904] (-) TimerEvent: {}
[5.011205] (-) TimerEvent: {}
[5.111534] (-) TimerEvent: {}
[5.211896] (-) TimerEvent: {}
[5.312352] (-) TimerEvent: {}
[5.412672] (-) TimerEvent: {}
[5.513069] (-) TimerEvent: {}
[5.613453] (-) TimerEvent: {}
[5.713956] (-) TimerEvent: {}
[5.814510] (-) TimerEvent: {}
[5.915216] (-) TimerEvent: {}
[6.015829] (-) TimerEvent: {}
[6.116458] (-) TimerEvent: {}
[6.216768] (-) TimerEvent: {}
[6.317119] (-) TimerEvent: {}
[6.417469] (-) TimerEvent: {}
[6.517834] (-) TimerEvent: {}
[6.618391] (-) TimerEvent: {}
[6.718815] (-) TimerEvent: {}
[6.819188] (-) TimerEvent: {}
[6.919563] (-) TimerEvent: {}
[7.020018] (-) TimerEvent: {}
[7.120334] (-) TimerEvent: {}
[7.220665] (-) TimerEvent: {}
[7.320988] (-) TimerEvent: {}
[7.421288] (-) TimerEvent: {}
[7.521614] (-) TimerEvent: {}
[7.621877] (-) TimerEvent: {}
[7.722194] (-) TimerEvent: {}
[7.822537] (-) TimerEvent: {}
[7.922882] (-) TimerEvent: {}
[8.023226] (-) TimerEvent: {}
[8.123569] (-) TimerEvent: {}
[8.223906] (-) TimerEvent: {}
[8.324227] (-) TimerEvent: {}
[8.424528] (-) TimerEvent: {}
[8.524880] (-) TimerEvent: {}
[8.625241] (-) TimerEvent: {}
[8.725585] (-) TimerEvent: {}
[8.825970] (-) TimerEvent: {}
[8.926325] (-) TimerEvent: {}
[9.026665] (-) TimerEvent: {}
[9.127010] (-) TimerEvent: {}
[9.227365] (-) TimerEvent: {}
[9.327712] (-) TimerEvent: {}
[9.428061] (-) TimerEvent: {}
[9.528410] (-) TimerEvent: {}
[9.628762] (-) TimerEvent: {}
[9.729111] (-) TimerEvent: {}
[9.829448] (-) TimerEvent: {}
[9.929792] (-) TimerEvent: {}
[10.030173] (-) TimerEvent: {}
[10.130516] (-) TimerEvent: {}
[10.230867] (-) TimerEvent: {}
[10.331213] (-) TimerEvent: {}
[10.431565] (-) TimerEvent: {}
[10.531902] (-) TimerEvent: {}
[10.632265] (-) TimerEvent: {}
[10.732620] (-) TimerEvent: {}
[10.832969] (-) TimerEvent: {}
[10.933313] (-) TimerEvent: {}
[11.033655] (-) TimerEvent: {}
[11.134004] (-) TimerEvent: {}
[11.234339] (-) TimerEvent: {}
[11.334633] (-) TimerEvent: {}
[11.434945] (-) TimerEvent: {}
[11.535222] (-) TimerEvent: {}
[11.635527] (-) TimerEvent: {}
[11.735860] (-) TimerEvent: {}
[11.836194] (-) TimerEvent: {}
[11.937185] (-) TimerEvent: {}
[12.038071] (-) TimerEvent: {}
[12.138405] (-) TimerEvent: {}
[12.238742] (-) TimerEvent: {}
[12.339078] (-) TimerEvent: {}
[12.439413] (-) TimerEvent: {}
[12.539753] (-) TimerEvent: {}
[12.640446] (-) TimerEvent: {}
[12.740789] (-) TimerEvent: {}
[12.841126] (-) TimerEvent: {}
[12.941468] (-) TimerEvent: {}
[13.041812] (-) TimerEvent: {}
[13.142199] (-) TimerEvent: {}
[13.242738] (-) TimerEvent: {}
[13.343208] (-) TimerEvent: {}
[13.444024] (-) TimerEvent: {}
[13.544526] (-) TimerEvent: {}
[13.644894] (-) TimerEvent: {}
[13.745307] (-) TimerEvent: {}
[13.845792] (-) TimerEvent: {}
[13.946229] (-) TimerEvent: {}
[14.046587] (-) TimerEvent: {}
[14.146881] (-) TimerEvent: {}
[14.247163] (-) TimerEvent: {}
[14.347459] (-) TimerEvent: {}
[14.447821] (-) TimerEvent: {}
[14.548190] (-) TimerEvent: {}
[14.648560] (-) TimerEvent: {}
[14.748971] (-) TimerEvent: {}
[14.849361] (-) TimerEvent: {}
[14.949787] (-) TimerEvent: {}
[15.050161] (-) TimerEvent: {}
[15.150505] (-) TimerEvent: {}
[15.250868] (-) TimerEvent: {}
[15.351221] (-) TimerEvent: {}
[15.451566] (-) TimerEvent: {}
[15.551924] (-) TimerEvent: {}
[15.652262] (-) TimerEvent: {}
[15.752619] (-) TimerEvent: {}
[15.853386] (-) TimerEvent: {}
[15.953724] (-) TimerEvent: {}
[16.054054] (-) TimerEvent: {}
[16.154404] (-) TimerEvent: {}
[16.254746] (-) TimerEvent: {}
[16.355059] (-) TimerEvent: {}
[16.455404] (-) TimerEvent: {}
[16.555731] (-) TimerEvent: {}
[16.656085] (-) TimerEvent: {}
[16.756444] (-) TimerEvent: {}
[16.856763] (-) TimerEvent: {}
[16.957098] (-) TimerEvent: {}
[17.057952] (-) TimerEvent: {}
[17.158818] (-) TimerEvent: {}
[17.259163] (-) TimerEvent: {}
[17.359510] (-) TimerEvent: {}
[17.459853] (-) TimerEvent: {}
[17.560883] (-) TimerEvent: {}
[17.661234] (-) TimerEvent: {}
[17.761578] (-) TimerEvent: {}
[17.861952] (-) TimerEvent: {}
[17.962305] (-) TimerEvent: {}
[18.062644] (-) TimerEvent: {}
[18.162975] (-) TimerEvent: {}
[18.263304] (-) TimerEvent: {}
[18.363641] (-) TimerEvent: {}
[18.464446] (-) TimerEvent: {}
[18.565282] (-) TimerEvent: {}
[18.665637] (-) TimerEvent: {}
[18.766031] (-) TimerEvent: {}
[18.866395] (-) TimerEvent: {}
[18.966753] (-) TimerEvent: {}
[19.067209] (-) TimerEvent: {}
[19.167767] (-) TimerEvent: {}
[19.268339] (-) TimerEvent: {}
[19.368741] (-) TimerEvent: {}
[19.469051] (-) TimerEvent: {}
[19.569348] (-) TimerEvent: {}
[19.669692] (-) TimerEvent: {}
[19.769966] (-) TimerEvent: {}
[19.870279] (-) TimerEvent: {}
[19.970698] (-) TimerEvent: {}
[20.070999] (-) TimerEvent: {}
[20.171293] (-) TimerEvent: {}
[20.271584] (-) TimerEvent: {}
[20.371860] (-) TimerEvent: {}
[20.472271] (-) TimerEvent: {}
[20.572622] (-) TimerEvent: {}
[20.672953] (-) TimerEvent: {}
[20.773221] (-) TimerEvent: {}
[20.873472] (-) TimerEvent: {}
[20.973753] (-) TimerEvent: {}
[21.074126] (-) TimerEvent: {}
[21.174457] (-) TimerEvent: {}
[21.274769] (-) TimerEvent: {}
[21.375054] (-) TimerEvent: {}
[21.475391] (-) TimerEvent: {}
[21.575720] (-) TimerEvent: {}
[21.676051] (-) TimerEvent: {}
[21.776383] (-) TimerEvent: {}
[21.876715] (-) TimerEvent: {}
[21.977512] (-) TimerEvent: {}
[22.077917] (-) TimerEvent: {}
[22.178303] (-) TimerEvent: {}
[22.278664] (-) TimerEvent: {}
[22.379029] (-) TimerEvent: {}
[22.479391] (-) TimerEvent: {}
[22.579750] (-) TimerEvent: {}
[22.680491] (-) TimerEvent: {}
[22.781250] (-) TimerEvent: {}
[22.881642] (-) TimerEvent: {}
[22.982006] (-) TimerEvent: {}
[23.082349] (-) TimerEvent: {}
[23.182683] (-) TimerEvent: {}
[23.283076] (-) TimerEvent: {}
[23.383441] (-) TimerEvent: {}
[23.483791] (-) TimerEvent: {}
[23.584144] (-) TimerEvent: {}
[23.684481] (-) TimerEvent: {}
[23.784821] (-) TimerEvent: {}
[23.885147] (-) TimerEvent: {}
[23.985479] (-) TimerEvent: {}
[24.085813] (-) TimerEvent: {}
[24.186177] (-) TimerEvent: {}
[24.286506] (-) TimerEvent: {}
[24.386838] (-) TimerEvent: {}
[24.487167] (-) TimerEvent: {}
[24.587501] (-) TimerEvent: {}
[24.688129] (-) TimerEvent: {}
[24.789001] (-) TimerEvent: {}
[24.889751] (-) TimerEvent: {}
[24.990088] (-) TimerEvent: {}
[25.090389] (-) TimerEvent: {}
[25.190717] (-) TimerEvent: {}
[25.291043] (-) TimerEvent: {}
[25.391372] (-) TimerEvent: {}
[25.491707] (-) TimerEvent: {}
[25.592032] (-) TimerEvent: {}
[25.692364] (-) TimerEvent: {}
[25.792691] (-) TimerEvent: {}
[25.893099] (-) TimerEvent: {}
[25.993578] (-) TimerEvent: {}
[26.093984] (-) TimerEvent: {}
[26.194611] (-) TimerEvent: {}
[26.295251] (-) TimerEvent: {}
[26.395702] (-) TimerEvent: {}
[26.496199] (-) TimerEvent: {}
[26.596675] (-) TimerEvent: {}
[26.697202] (-) TimerEvent: {}
[26.797524] (-) TimerEvent: {}
[26.897940] (-) TimerEvent: {}
[26.998305] (-) TimerEvent: {}
[27.098736] (-) TimerEvent: {}
[27.199092] (-) TimerEvent: {}
[27.299438] (-) TimerEvent: {}
[27.399728] (-) TimerEvent: {}
[27.500038] (-) TimerEvent: {}
[27.600317] (-) TimerEvent: {}
[27.700614] (-) TimerEvent: {}
[27.800891] (-) TimerEvent: {}
[27.901236] (-) TimerEvent: {}
[28.001572] (-) TimerEvent: {}
[28.101887] (-) TimerEvent: {}
[28.202262] (-) TimerEvent: {}
[28.302626] (-) TimerEvent: {}
[28.403001] (-) TimerEvent: {}
[28.503427] (-) TimerEvent: {}
[28.603760] (-) TimerEvent: {}
[28.705034] (-) TimerEvent: {}
[28.805363] (-) TimerEvent: {}
[28.905695] (-) TimerEvent: {}
[29.006381] (-) TimerEvent: {}
[29.106721] (-) TimerEvent: {}
[29.207027] (-) TimerEvent: {}
[29.307360] (-) TimerEvent: {}
[29.407695] (-) TimerEvent: {}
[29.508030] (-) TimerEvent: {}
[29.608788] (-) TimerEvent: {}
[29.709503] (-) TimerEvent: {}
[29.809834] (-) TimerEvent: {}
[29.910385] (-) TimerEvent: {}
[30.010717] (-) TimerEvent: {}
[30.111051] (-) TimerEvent: {}
[30.211375] (-) TimerEvent: {}
[30.311709] (-) TimerEvent: {}
[30.412045] (-) TimerEvent: {}
[30.512754] (-) TimerEvent: {}
[30.613089] (-) TimerEvent: {}
[30.713420] (-) TimerEvent: {}
[30.813748] (-) TimerEvent: {}
[30.914375] (-) TimerEvent: {}
[31.014707] (-) TimerEvent: {}
[31.115081] (-) TimerEvent: {}
[31.215451] (-) TimerEvent: {}
[31.315809] (-) TimerEvent: {}
[31.416138] (-) TimerEvent: {}
[31.516465] (-) TimerEvent: {}
[31.616796] (-) TimerEvent: {}
[31.717108] (-) TimerEvent: {}
[31.817385] (-) TimerEvent: {}
[31.917711] (-) TimerEvent: {}
[32.018007] (-) TimerEvent: {}
[32.118344] (-) TimerEvent: {}
[32.218664] (-) TimerEvent: {}
[32.318976] (-) TimerEvent: {}
[32.419327] (-) TimerEvent: {}
[32.519761] (-) TimerEvent: {}
[32.620098] (-) TimerEvent: {}
[32.720464] (-) TimerEvent: {}
[32.821359] (-) TimerEvent: {}
[32.921795] (-) TimerEvent: {}
[33.022397] (-) TimerEvent: {}
[33.122995] (-) TimerEvent: {}
[33.223526] (-) TimerEvent: {}
[33.324041] (-) TimerEvent: {}
[33.424508] (-) TimerEvent: {}
[33.524938] (-) TimerEvent: {}
[33.625279] (-) TimerEvent: {}
[33.725630] (-) TimerEvent: {}
[33.826040] (-) TimerEvent: {}
[33.926384] (-) TimerEvent: {}
[34.026659] (-) TimerEvent: {}
[34.126927] (-) TimerEvent: {}
[34.227176] (-) TimerEvent: {}
[34.327465] (-) TimerEvent: {}
[34.427717] (-) TimerEvent: {}
[34.527970] (-) TimerEvent: {}
[34.628223] (-) TimerEvent: {}
[34.729343] (-) TimerEvent: {}
[34.829677] (-) TimerEvent: {}
[34.930013] (-) TimerEvent: {}
[35.030345] (-) TimerEvent: {}
[35.130676] (-) TimerEvent: {}
[35.231053] (-) TimerEvent: {}
[35.331446] (-) TimerEvent: {}
[35.431849] (-) TimerEvent: {}
[35.532188] (-) TimerEvent: {}
[35.632553] (-) TimerEvent: {}
[35.732945] (-) TimerEvent: {}
[35.833249] (-) TimerEvent: {}
[35.933602] (-) TimerEvent: {}
[36.033896] (-) TimerEvent: {}
[36.134299] (-) TimerEvent: {}
[36.234667] (-) TimerEvent: {}
[36.335005] (-) TimerEvent: {}
[36.435335] (-) TimerEvent: {}
[36.535671] (-) TimerEvent: {}
[36.635998] (-) TimerEvent: {}
[36.736340] (-) TimerEvent: {}
[36.836667] (-) TimerEvent: {}
[36.936993] (-) TimerEvent: {}
[37.037329] (-) TimerEvent: {}
[37.137658] (-) TimerEvent: {}
[37.237990] (-) TimerEvent: {}
[37.338396] (-) TimerEvent: {}
[37.438730] (-) TimerEvent: {}
[37.539056] (-) TimerEvent: {}
[37.639389] (-) TimerEvent: {}
[37.739713] (-) TimerEvent: {}
[37.840040] (-) TimerEvent: {}
[37.940364] (-) TimerEvent: {}
[38.040696] (-) TimerEvent: {}
[38.141016] (-) TimerEvent: {}
[38.241339] (-) TimerEvent: {}
[38.341667] (-) TimerEvent: {}
[38.441966] (-) TimerEvent: {}
[38.542268] (-) TimerEvent: {}
[38.642790] (-) TimerEvent: {}
[38.743118] (-) TimerEvent: {}
[38.843534] (-) TimerEvent: {}
[38.944147] (-) TimerEvent: {}
[39.044825] (-) TimerEvent: {}
[39.145153] (-) TimerEvent: {}
[39.245517] (-) TimerEvent: {}
[39.345837] (-) TimerEvent: {}
[39.446126] (-) TimerEvent: {}
[39.546429] (-) TimerEvent: {}
[39.646720] (-) TimerEvent: {}
[39.747022] (-) TimerEvent: {}
[39.847362] (-) TimerEvent: {}
[39.947717] (-) TimerEvent: {}
[40.048061] (-) TimerEvent: {}
[40.148586] (-) TimerEvent: {}
[40.249399] (-) TimerEvent: {}
[40.349918] (-) TimerEvent: {}
[40.450275] (-) TimerEvent: {}
[40.550672] (-) TimerEvent: {}
[40.651069] (-) TimerEvent: {}
[40.751498] (-) TimerEvent: {}
[40.851875] (-) TimerEvent: {}
[40.952158] (-) TimerEvent: {}
[41.052652] (-) TimerEvent: {}
[41.152940] (-) TimerEvent: {}
[41.253262] (-) TimerEvent: {}
[41.353565] (-) TimerEvent: {}
[41.453914] (-) TimerEvent: {}
[41.554184] (-) TimerEvent: {}
[41.654532] (-) TimerEvent: {}
[41.754849] (-) TimerEvent: {}
[41.855135] (-) TimerEvent: {}
[41.955452] (-) TimerEvent: {}
[42.055718] (-) TimerEvent: {}
[42.155998] (-) TimerEvent: {}
[42.256345] (-) TimerEvent: {}
[42.356690] (-) TimerEvent: {}
[42.457010] (-) TimerEvent: {}
[42.557303] (-) TimerEvent: {}
[42.657648] (-) TimerEvent: {}
[42.758003] (-) TimerEvent: {}
[42.858871] (-) TimerEvent: {}
[42.959206] (-) TimerEvent: {}
[43.059539] (-) TimerEvent: {}
[43.159867] (-) TimerEvent: {}
[43.260761] (-) TimerEvent: {}
[43.361575] (-) TimerEvent: {}
[43.462373] (-) TimerEvent: {}
[43.562707] (-) TimerEvent: {}
[43.663043] (-) TimerEvent: {}
[43.763383] (-) TimerEvent: {}
[43.863725] (-) TimerEvent: {}
[43.964049] (-) TimerEvent: {}
[44.064816] (-) TimerEvent: {}
[44.165575] (-) TimerEvent: {}
[44.265980] (-) TimerEvent: {}
[44.366379] (-) TimerEvent: {}
[44.466751] (-) TimerEvent: {}
[44.472816] (point_lio_ros2) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable pointlio_mapping\x1b[0m\n'}
[44.566922] (-) TimerEvent: {}
[44.667321] (-) TimerEvent: {}
[44.767701] (-) TimerEvent: {}
[44.868079] (-) TimerEvent: {}
[44.968459] (-) TimerEvent: {}
[45.068841] (-) TimerEvent: {}
[45.169219] (-) TimerEvent: {}
[45.270039] (-) TimerEvent: {}
[45.282035] (point_lio_ros2) StdoutLine: {'line': b'[100%] Built target pointlio_mapping\n'}
[45.293507] (point_lio_ros2) CommandEnded: {'returncode': 0}
[45.294345] (point_lio_ros2) JobProgress: {'identifier': 'point_lio_ros2', 'progress': 'install'}
[45.304441] (point_lio_ros2) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2'], 'cwd': '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7897/'), ('no_proxy', 'localhost,127.0.0.1,***********/16,10.0.0.0/8,**********/12,::1'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'wuliu'), ('all_proxy', 'socks://127.0.0.1:7897/'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('TERM_PROGRAM_VERSION', '1.103.1'), ('DESKTOP_SESSION', 'ubuntu'), ('NO_PROXY', 'localhost,127.0.0.1,***********/16,10.0.0.0/8,**********/12,::1'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('SYSTEMD_EXEC_PID', '17594'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('https_proxy', 'http://127.0.0.1:7897/'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'wuliu'), ('ALL_PROXY', 'socks://127.0.0.1:7897/'), ('http_proxy', 'http://127.0.0.1:7897/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'wuliu'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/wuliu:@/tmp/.ICE-unix/1331,unix/wuliu:/tmp/.ICE-unix/1331'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/6d8d3855_5aa0_48a5_9891_86d77e43fc8e'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.ARYRA3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-2157ec0450.sock'), ('GNOME_TERMINAL_SERVICE', ':1.132'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7897/'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[45.311724] (point_lio_ros2) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[45.312116] (point_lio_ros2) StdoutLine: {'line': b'-- Execute custom install script\n'}
[45.312384] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/lib/point_lio_ros2/pointlio_mapping\n'}
[45.321878] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/config/avia.yaml\n'}
[45.330364] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/config/mid360.yaml\n'}
[45.339643] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/launch/gdb_debug_example.launch.py\n'}
[45.349252] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/launch/mapping_avia.launch.py\n'}
[45.358646] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/launch/mapping_mid360.launch.py\n'}
[45.367357] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/rviz_cfg/loam_livox.rviz\n'}
[45.370135] (-) TimerEvent: {}
[45.376291] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/ament_index/resource_index/package_run_dependencies/point_lio_ros2\n'}
[45.384590] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/ament_index/resource_index/parent_prefix_path/point_lio_ros2\n'}
[45.393272] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/environment/ament_prefix_path.sh\n'}
[45.401237] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/environment/ament_prefix_path.dsv\n'}
[45.409589] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/environment/path.sh\n'}
[45.417832] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/environment/path.dsv\n'}
[45.425845] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/local_setup.bash\n'}
[45.433884] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/local_setup.sh\n'}
[45.441668] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/local_setup.zsh\n'}
[45.450266] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/local_setup.dsv\n'}
[45.458272] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/package.dsv\n'}
[45.467426] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/ament_index/resource_index/packages/point_lio_ros2\n'}
[45.470238] (-) TimerEvent: {}
[45.476484] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[45.484845] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/cmake/point_lio_ros2Config.cmake\n'}
[45.492934] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/cmake/point_lio_ros2Config-version.cmake\n'}
[45.501434] (point_lio_ros2) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/package.xml\n'}
[45.513119] (point_lio_ros2) CommandEnded: {'returncode': 0}
[45.535639] (point_lio_ros2) JobEnded: {'identifier': 'point_lio_ros2', 'rc': 0}
[45.536343] (-) EventReactorShutdown: {}
