[0.089s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'point_lio_ros2', '--symlink-install']
[0.089s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['point_lio_ros2'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7f6cf4737340>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7f6cf4736ef0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7f6cf4736ef0>>)
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.220s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/code/ros2_ws/pointlio'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(src/Point-LIO) by extensions ['ignore', 'ignore_ament_install']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/Point-LIO) by extension 'ignore'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/Point-LIO) by extension 'ignore_ament_install'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/Point-LIO) by extensions ['colcon_pkg']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/Point-LIO) by extension 'colcon_pkg'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/Point-LIO) by extensions ['colcon_meta']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/Point-LIO) by extension 'colcon_meta'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/Point-LIO) by extensions ['ros']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(src/Point-LIO) by extension 'ros'
[0.236s] DEBUG:colcon.colcon_core.package_identification:Package 'src/Point-LIO' with type 'ros.catkin' and name 'point_lio'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_lio_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_lio_ros2) by extension 'ignore'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_lio_ros2) by extension 'ignore_ament_install'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_lio_ros2) by extensions ['colcon_pkg']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_lio_ros2) by extension 'colcon_pkg'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_lio_ros2) by extensions ['colcon_meta']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_lio_ros2) by extension 'colcon_meta'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_lio_ros2) by extensions ['ros']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_lio_ros2) by extension 'ros'
[0.238s] DEBUG:colcon.colcon_core.package_identification:Package 'src/point_lio_ros2' with type 'ros.ament_cmake' and name 'point_lio_ros2'
[0.238s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.238s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.238s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.238s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.238s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.254s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'point_lio' in 'src/Point-LIO'
[0.254s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.254s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.256s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 272 installed packages in /opt/ros/humble
[0.257s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.301s] Level 5:colcon.colcon_core.verb:set package 'point_lio_ros2' build argument 'cmake_args' from command line to 'None'
[0.301s] Level 5:colcon.colcon_core.verb:set package 'point_lio_ros2' build argument 'cmake_target' from command line to 'None'
[0.301s] Level 5:colcon.colcon_core.verb:set package 'point_lio_ros2' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.301s] Level 5:colcon.colcon_core.verb:set package 'point_lio_ros2' build argument 'cmake_clean_cache' from command line to 'False'
[0.301s] Level 5:colcon.colcon_core.verb:set package 'point_lio_ros2' build argument 'cmake_clean_first' from command line to 'False'
[0.301s] Level 5:colcon.colcon_core.verb:set package 'point_lio_ros2' build argument 'cmake_force_configure' from command line to 'False'
[0.301s] Level 5:colcon.colcon_core.verb:set package 'point_lio_ros2' build argument 'ament_cmake_args' from command line to 'None'
[0.301s] Level 5:colcon.colcon_core.verb:set package 'point_lio_ros2' build argument 'catkin_cmake_args' from command line to 'None'
[0.301s] Level 5:colcon.colcon_core.verb:set package 'point_lio_ros2' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.301s] DEBUG:colcon.colcon_core.verb:Building package 'point_lio_ros2' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2', 'merge_install': False, 'path': '/home/<USER>/code/ros2_ws/pointlio/src/point_lio_ros2', 'symlink_install': True, 'test_result_base': None}
[0.302s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.303s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.303s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/ros2_ws/pointlio/src/point_lio_ros2' with build type 'ament_cmake'
[0.303s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/ros2_ws/pointlio/src/point_lio_ros2'
[0.314s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.314s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.314s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.321s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/ros2_ws/pointlio/src/point_lio_ros2 -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2
[3.778s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/ros2_ws/pointlio/src/point_lio_ros2 -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2
[3.781s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2 -- -j16 -l16
[45.597s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2 -- -j16 -l16
[45.609s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2
[45.816s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(point_lio_ros2)
[45.817s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2
[45.828s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2' for CMake module files
[45.829s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2' for CMake config files
[45.829s] Level 1:colcon.colcon_core.shell:create_environment_hook('point_lio_ros2', 'cmake_prefix_path')
[45.829s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/hook/cmake_prefix_path.ps1'
[45.830s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/hook/cmake_prefix_path.dsv'
[45.831s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/hook/cmake_prefix_path.sh'
[45.831s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/lib'
[45.831s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/bin'
[45.831s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/lib/pkgconfig/point_lio_ros2.pc'
[45.832s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/lib/python3.10/site-packages'
[45.832s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/bin'
[45.832s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/package.ps1'
[45.832s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/package.dsv'
[45.833s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/package.sh'
[45.833s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/package.bash'
[45.834s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/package.zsh'
[45.834s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/colcon-core/packages/point_lio_ros2)
[45.835s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(point_lio_ros2)
[45.835s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2' for CMake module files
[45.835s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2' for CMake config files
[45.836s] Level 1:colcon.colcon_core.shell:create_environment_hook('point_lio_ros2', 'cmake_prefix_path')
[45.836s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/hook/cmake_prefix_path.ps1'
[45.836s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/hook/cmake_prefix_path.dsv'
[45.836s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/hook/cmake_prefix_path.sh'
[45.837s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/lib'
[45.837s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/bin'
[45.837s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/lib/pkgconfig/point_lio_ros2.pc'
[45.837s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/lib/python3.10/site-packages'
[45.837s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/bin'
[45.837s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/package.ps1'
[45.838s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/package.dsv'
[45.838s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/package.sh'
[45.838s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/package.bash'
[45.838s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/package.zsh'
[45.839s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/colcon-core/packages/point_lio_ros2)
[45.839s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[45.839s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[45.839s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[45.839s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[45.844s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[45.844s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[45.844s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[45.857s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[45.858s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/ros2_ws/pointlio/install/local_setup.ps1'
[45.859s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/ros2_ws/pointlio/install/_local_setup_util_ps1.py'
[45.860s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/ros2_ws/pointlio/install/setup.ps1'
[45.861s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/ros2_ws/pointlio/install/local_setup.sh'
[45.862s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/ros2_ws/pointlio/install/_local_setup_util_sh.py'
[45.863s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/ros2_ws/pointlio/install/setup.sh'
[45.864s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/ros2_ws/pointlio/install/local_setup.bash'
[45.864s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/ros2_ws/pointlio/install/setup.bash'
[45.865s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/ros2_ws/pointlio/install/local_setup.zsh'
[45.865s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/ros2_ws/pointlio/install/setup.zsh'
