-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Current CPU architecture: x86_64
-- Processor number:  16
-- core for MP:  3
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
-- Found FastRTPS: /opt/ros/humble/include  
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Found rclpy: 3.3.17 (/opt/ros/humble/share/rclpy/cmake)
-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
-- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)
-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
-- Found pcl_ros: 2.4.5 (/opt/ros/humble/share/pcl_ros/cmake)
-- Checking for module 'eigen3'
--   Found eigen3, version 3.4.0
-- Found Eigen: /usr/include/eigen3 (Required is at least version "3.1") 
-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
-- Checking for module 'flann'
--   Found flann, version 1.9.1
-- Found FLANN: /usr/lib/x86_64-linux-gnu/libflann_cpp.so  
-- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)
-- Checking for module 'libusb-1.0'
--   Found libusb-1.0, version 1.0.25
-- Found libusb: /usr/lib/x86_64-linux-gnu/libusb-1.0.so  
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
-- Found Qhull version 8.0.2
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- Found PCL_COMMON: /usr/lib/x86_64-linux-gnu/libpcl_common.so  
-- Found PCL_KDTREE: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so  
-- Found PCL_OCTREE: /usr/lib/x86_64-linux-gnu/libpcl_octree.so  
-- Found PCL_SEARCH: /usr/lib/x86_64-linux-gnu/libpcl_search.so  
-- Found PCL_SAMPLE_CONSENSUS: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so  
-- Found PCL_FILTERS: /usr/lib/x86_64-linux-gnu/libpcl_filters.so  
-- Found PCL_2D: /usr/include/pcl-1.12  
-- Found PCL_GEOMETRY: /usr/include/pcl-1.12  
-- Found PCL_IO: /usr/lib/x86_64-linux-gnu/libpcl_io.so  
-- Found PCL_FEATURES: /usr/lib/x86_64-linux-gnu/libpcl_features.so  
-- Found PCL_ML: /usr/lib/x86_64-linux-gnu/libpcl_ml.so  
-- Found PCL_SEGMENTATION: /usr/lib/x86_64-linux-gnu/libpcl_segmentation.so  
-- Found PCL_VISUALIZATION: /usr/lib/x86_64-linux-gnu/libpcl_visualization.so  
-- Found PCL_SURFACE: /usr/lib/x86_64-linux-gnu/libpcl_surface.so  
-- Found PCL_REGISTRATION: /usr/lib/x86_64-linux-gnu/libpcl_registration.so  
-- Found PCL_KEYPOINTS: /usr/lib/x86_64-linux-gnu/libpcl_keypoints.so  
-- Found PCL_TRACKING: /usr/lib/x86_64-linux-gnu/libpcl_tracking.so  
-- Found PCL_RECOGNITION: /usr/lib/x86_64-linux-gnu/libpcl_recognition.so  
-- Found PCL_STEREO: /usr/lib/x86_64-linux-gnu/libpcl_stereo.so  
-- Found PCL_APPS: /usr/lib/x86_64-linux-gnu/libpcl_apps.so  
-- Found PCL_IN_HAND_SCANNER: /usr/include/pcl-1.12  
-- Found PCL_MODELER: /usr/include/pcl-1.12  
-- Found PCL_POINT_CLOUD_EDITOR: /usr/include/pcl-1.12  
-- Found PCL_OUTOFCORE: /usr/lib/x86_64-linux-gnu/libpcl_outofcore.so  
-- Found PCL_PEOPLE: /usr/lib/x86_64-linux-gnu/libpcl_people.so  
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Found Eigen3: TRUE (found version "3.4.0") 
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found visualization_msgs: 4.9.0 (/opt/ros/humble/share/visualization_msgs/cmake)
-- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found version "3.10.12") 
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2
[ 83%] [32mBuilding CXX object CMakeFiles/pointlio_mapping.dir/include/ikd-Tree/ikd_Tree.cpp.o[0m
[ 83%] [32mBuilding CXX object CMakeFiles/pointlio_mapping.dir/src/Estimator.cpp.o[0m
[ 83%] [32mBuilding CXX object CMakeFiles/pointlio_mapping.dir/src/parameters.cpp.o[0m
[ 83%] [32mBuilding CXX object CMakeFiles/pointlio_mapping.dir/src/preprocess.cpp.o[0m
[ 83%] [32mBuilding CXX object CMakeFiles/pointlio_mapping.dir/src/laserMapping.cpp.o[0m
[100%] [32m[1mLinking CXX executable pointlio_mapping[0m
[100%] Built target pointlio_mapping
-- Install configuration: ""
-- Execute custom install script
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/lib/point_lio_ros2/pointlio_mapping
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/config/avia.yaml
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/config/mid360.yaml
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/launch/gdb_debug_example.launch.py
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/launch/mapping_avia.launch.py
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/launch/mapping_mid360.launch.py
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/rviz_cfg/loam_livox.rviz
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/ament_index/resource_index/package_run_dependencies/point_lio_ros2
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/ament_index/resource_index/parent_prefix_path/point_lio_ros2
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/environment/ament_prefix_path.sh
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/environment/ament_prefix_path.dsv
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/environment/path.sh
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/environment/path.dsv
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/local_setup.bash
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/local_setup.sh
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/local_setup.zsh
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/local_setup.dsv
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/package.dsv
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/ament_index/resource_index/packages/point_lio_ros2
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/cmake/ament_cmake_export_dependencies-extras.cmake
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/cmake/point_lio_ros2Config.cmake
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/cmake/point_lio_ros2Config-version.cmake
-- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/package.xml
