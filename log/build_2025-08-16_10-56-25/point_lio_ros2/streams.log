[0.018s] Invoking command in '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/ros2_ws/pointlio/src/point_lio_ros2 -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2
[0.107s] -- The C compiler identification is GNU 11.4.0
[0.179s] -- The CXX compiler identification is GNU 11.4.0
[0.189s] -- Detecting C compiler ABI info
[0.272s] -- Detecting C compiler ABI info - done
[0.278s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.278s] -- Detecting C compile features
[0.279s] -- Detecting C compile features - done
[0.282s] -- Detecting CXX compiler ABI info
[0.370s] -- Detecting CXX compiler ABI info - done
[0.376s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.376s] -- Detecting CXX compile features
[0.376s] -- Detecting CXX compile features - done
[0.377s] -- Current CPU architecture: x86_64
[0.384s] -- Processor number:  16
[0.384s] -- core for MP:  3
[0.388s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.545s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.602s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.627s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.665s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.669s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.676s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.687s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.701s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.740s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.742s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.834s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.857s] -- Found FastRTPS: /opt/ros/humble/include  
[0.894s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.904s] -- Looking for pthread.h
[0.977s] -- Looking for pthread.h - found
[0.978s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[1.050s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[1.050s] -- Found Threads: TRUE  
[1.095s] -- Found rclpy: 3.3.17 (/opt/ros/humble/share/rclpy/cmake)
[1.096s] -- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[1.108s] -- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)
[1.125s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[1.141s] -- Found pcl_ros: 2.4.5 (/opt/ros/humble/share/pcl_ros/cmake)
[1.181s] -- Checking for module 'eigen3'
[1.198s] --   Found eigen3, version 3.4.0
[1.236s] -- Found Eigen: /usr/include/eigen3 (Required is at least version "3.1") 
[1.236s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[1.269s] -- Checking for module 'flann'
[1.286s] --   Found flann, version 1.9.1
[1.330s] -- Found FLANN: /usr/lib/x86_64-linux-gnu/libflann_cpp.so  
[1.330s] -- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)
[2.229s] -- Checking for module 'libusb-1.0'
[2.245s] --   Found libusb-1.0, version 1.0.25
[2.295s] -- Found libusb: /usr/lib/x86_64-linux-gnu/libusb-1.0.so  
[2.297s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[2.366s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
[2.490s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[2.496s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[2.503s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
[2.626s] -- Found Qhull version 8.0.2
[2.743s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[2.855s] -- Found PCL_COMMON: /usr/lib/x86_64-linux-gnu/libpcl_common.so  
[2.856s] -- Found PCL_KDTREE: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so  
[2.857s] -- Found PCL_OCTREE: /usr/lib/x86_64-linux-gnu/libpcl_octree.so  
[2.857s] -- Found PCL_SEARCH: /usr/lib/x86_64-linux-gnu/libpcl_search.so  
[2.858s] -- Found PCL_SAMPLE_CONSENSUS: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so  
[2.858s] -- Found PCL_FILTERS: /usr/lib/x86_64-linux-gnu/libpcl_filters.so  
[2.859s] -- Found PCL_2D: /usr/include/pcl-1.12  
[2.859s] -- Found PCL_GEOMETRY: /usr/include/pcl-1.12  
[2.859s] -- Found PCL_IO: /usr/lib/x86_64-linux-gnu/libpcl_io.so  
[2.860s] -- Found PCL_FEATURES: /usr/lib/x86_64-linux-gnu/libpcl_features.so  
[2.861s] -- Found PCL_ML: /usr/lib/x86_64-linux-gnu/libpcl_ml.so  
[2.861s] -- Found PCL_SEGMENTATION: /usr/lib/x86_64-linux-gnu/libpcl_segmentation.so  
[2.862s] -- Found PCL_VISUALIZATION: /usr/lib/x86_64-linux-gnu/libpcl_visualization.so  
[2.863s] -- Found PCL_SURFACE: /usr/lib/x86_64-linux-gnu/libpcl_surface.so  
[2.863s] -- Found PCL_REGISTRATION: /usr/lib/x86_64-linux-gnu/libpcl_registration.so  
[2.864s] -- Found PCL_KEYPOINTS: /usr/lib/x86_64-linux-gnu/libpcl_keypoints.so  
[2.865s] -- Found PCL_TRACKING: /usr/lib/x86_64-linux-gnu/libpcl_tracking.so  
[2.865s] -- Found PCL_RECOGNITION: /usr/lib/x86_64-linux-gnu/libpcl_recognition.so  
[2.866s] -- Found PCL_STEREO: /usr/lib/x86_64-linux-gnu/libpcl_stereo.so  
[2.867s] -- Found PCL_APPS: /usr/lib/x86_64-linux-gnu/libpcl_apps.so  
[2.867s] -- Found PCL_IN_HAND_SCANNER: /usr/include/pcl-1.12  
[2.868s] -- Found PCL_MODELER: /usr/include/pcl-1.12  
[2.868s] -- Found PCL_POINT_CLOUD_EDITOR: /usr/include/pcl-1.12  
[2.869s] -- Found PCL_OUTOFCORE: /usr/lib/x86_64-linux-gnu/libpcl_outofcore.so  
[2.869s] -- Found PCL_PEOPLE: /usr/lib/x86_64-linux-gnu/libpcl_people.so  
[2.905s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[2.906s] -- Found Eigen3: TRUE (found version "3.4.0") 
[2.906s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[2.983s] -- Found visualization_msgs: 4.9.0 (/opt/ros/humble/share/visualization_msgs/cmake)
[3.318s] -- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found version "3.10.12") 
[3.379s] -- Configuring done
[3.448s] -- Generating done
[3.457s] -- Build files have been written to: /home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2
[3.475s] Invoked command in '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/ros2_ws/pointlio/src/point_lio_ros2 -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2
[3.477s] Invoking command in '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2 -- -j16 -l16
[3.529s] [ 83%] [32mBuilding CXX object CMakeFiles/pointlio_mapping.dir/include/ikd-Tree/ikd_Tree.cpp.o[0m
[3.529s] [ 83%] [32mBuilding CXX object CMakeFiles/pointlio_mapping.dir/src/Estimator.cpp.o[0m
[3.529s] [ 83%] [32mBuilding CXX object CMakeFiles/pointlio_mapping.dir/src/parameters.cpp.o[0m
[3.529s] [ 83%] [32mBuilding CXX object CMakeFiles/pointlio_mapping.dir/src/preprocess.cpp.o[0m
[3.529s] [ 83%] [32mBuilding CXX object CMakeFiles/pointlio_mapping.dir/src/laserMapping.cpp.o[0m
[44.473s] [100%] [32m[1mLinking CXX executable pointlio_mapping[0m
[45.282s] [100%] Built target pointlio_mapping
[45.294s] Invoked command in '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2 -- -j16 -l16
[45.305s] Invoking command in '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2
[45.312s] -- Install configuration: ""
[45.312s] -- Execute custom install script
[45.312s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/lib/point_lio_ros2/pointlio_mapping
[45.322s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/config/avia.yaml
[45.330s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/config/mid360.yaml
[45.340s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/launch/gdb_debug_example.launch.py
[45.349s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/launch/mapping_avia.launch.py
[45.359s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/launch/mapping_mid360.launch.py
[45.367s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/rviz_cfg/loam_livox.rviz
[45.376s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/ament_index/resource_index/package_run_dependencies/point_lio_ros2
[45.385s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/ament_index/resource_index/parent_prefix_path/point_lio_ros2
[45.393s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/environment/ament_prefix_path.sh
[45.401s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/environment/ament_prefix_path.dsv
[45.410s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/environment/path.sh
[45.418s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/environment/path.dsv
[45.426s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/local_setup.bash
[45.434s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/local_setup.sh
[45.442s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/local_setup.zsh
[45.450s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/local_setup.dsv
[45.458s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/package.dsv
[45.467s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/ament_index/resource_index/packages/point_lio_ros2
[45.476s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/cmake/ament_cmake_export_dependencies-extras.cmake
[45.485s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/cmake/point_lio_ros2Config.cmake
[45.493s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/cmake/point_lio_ros2Config-version.cmake
[45.501s] -- Symlinking: /home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2/share/point_lio_ros2/package.xml
[45.513s] Invoked command in '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2
