Invoking command in '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/ros2_ws/pointlio/src/point_lio_ros2 -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2
Invoked command in '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/ros2_ws/pointlio/src/point_lio_ros2 -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/ros2_ws/pointlio/install/point_lio_ros2
Invoking command in '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2 -- -j16 -l16
Invoked command in '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2 -- -j16 -l16
Invoking command in '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2
Invoked command in '/home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/ros2_ws/pointlio/build/point_lio_ros2
