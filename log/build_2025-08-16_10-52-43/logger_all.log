[0.086s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'point_lio', '--symlink-install']
[0.086s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['point_lio'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7fc02e33b340>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7fc02e33aef0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7fc02e33aef0>>)
[0.213s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.213s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.213s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.213s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.213s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.213s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.213s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/code/ros2_ws/pointlio'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/Point-LIO) by extensions ['ignore', 'ignore_ament_install']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/Point-LIO) by extension 'ignore'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/Point-LIO) by extension 'ignore_ament_install'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/Point-LIO) by extensions ['colcon_pkg']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/Point-LIO) by extension 'colcon_pkg'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/Point-LIO) by extensions ['colcon_meta']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/Point-LIO) by extension 'colcon_meta'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/Point-LIO) by extensions ['ros']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(src/Point-LIO) by extension 'ros'
[0.230s] DEBUG:colcon.colcon_core.package_identification:Package 'src/Point-LIO' with type 'ros.catkin' and name 'point_lio'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_lio_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_lio_ros2) by extension 'ignore'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_lio_ros2) by extension 'ignore_ament_install'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_lio_ros2) by extensions ['colcon_pkg']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_lio_ros2) by extension 'colcon_pkg'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_lio_ros2) by extensions ['colcon_meta']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_lio_ros2) by extension 'colcon_meta'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_lio_ros2) by extensions ['ros']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_lio_ros2) by extension 'ros'
[0.232s] DEBUG:colcon.colcon_core.package_identification:Package 'src/point_lio_ros2' with type 'ros.ament_cmake' and name 'point_lio'
[0.232s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.232s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.232s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.232s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.232s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.251s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.251s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.253s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 272 installed packages in /opt/ros/humble
[0.257s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.309s] ERROR:colcon:colcon build: Duplicate package names not supported:
- point_lio:
  - src/Point-LIO
  - src/point_lio_ros2
